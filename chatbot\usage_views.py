"""
Usage Dashboard Views

API endpoints for OpenAI usage tracking and analytics.
"""

import csv
from datetime import datetime, timedelta
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, Count, Avg, Q, DateField
from django.db.models.functions import TruncDate
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import status

from .models import OpenAIUsage
from .openai_usage_tracker import get_usage_summary


@api_view(['GET'])
@permission_classes([IsAdminUser])
def usage_summary(request):
    """
    Get usage summary statistics with optional date filtering.
    
    Query parameters:
    - start_date: YYYY-MM-DD format (optional)
    - end_date: YYYY-MM-DD format (optional)
    - days: Number of days back from today (optional, overrides start_date)
    """
    try:
        # Parse query parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        days = request.GET.get('days')
        
        # Handle date filtering
        if days:
            try:
                days_int = int(days)
                start_date = timezone.now() - timedelta(days=days_int)
                end_date = None
            except ValueError:
                return Response(
                    {"error": "Invalid 'days' parameter. Must be an integer."}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            if start_date:
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d')
                    start_date = timezone.make_aware(start_date)
                except ValueError:
                    return Response(
                        {"error": "Invalid start_date format. Use YYYY-MM-DD."}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            if end_date:
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d')
                    end_date = timezone.make_aware(end_date)
                    # Set to end of day
                    end_date = end_date.replace(hour=23, minute=59, second=59)
                except ValueError:
                    return Response(
                        {"error": "Invalid end_date format. Use YYYY-MM-DD."}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
        
        # Get summary data
        try:
            summary = get_usage_summary(start_date, end_date)
        except Exception as e:
            return Response(
                {"error": f"Failed to get usage summary: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Get additional breakdown by model and API type
        try:
            queryset = OpenAIUsage.objects.all()
            if start_date:
                queryset = queryset.filter(request_timestamp__gte=start_date)
            if end_date:
                queryset = queryset.filter(request_timestamp__lte=end_date)

            # Model breakdown
            model_breakdown = (
                queryset.values('model_name')
                .annotate(
                    total_requests=Count('id'),
                    total_tokens_sum=Sum('total_tokens'),
                    total_cost=Sum('cost_usd')
                )
                .order_by('-total_cost')
            )

            # API type breakdown
            api_type_breakdown = (
                queryset.values('api_type')
                .annotate(
                    total_requests=Count('id'),
                    total_tokens_sum=Sum('total_tokens'),
                    total_cost=Sum('cost_usd')
                )
                .order_by('-total_cost')
            )
        except Exception as e:
            # Fallback to empty breakdowns if there's an error
            model_breakdown = []
            api_type_breakdown = []
        
        # Daily usage for the last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)
        try:
            # Use Django's date truncation for better database compatibility
            daily_usage = (
                OpenAIUsage.objects.filter(request_timestamp__gte=thirty_days_ago)
                .annotate(day=TruncDate('request_timestamp'))
                .values('day')
                .annotate(
                    total_requests=Count('id'),
                    total_tokens_sum=Sum('total_tokens'),
                    total_cost=Sum('cost_usd')
                )
                .order_by('day')
            )
        except Exception as e:
            # Fallback: just return empty daily usage if there's an issue
            daily_usage = []
        
        # Convert Decimal values to float for JSON serialization and fix field names
        def convert_decimals(data):
            if isinstance(data, list):
                return [convert_decimals(item) for item in data]
            elif isinstance(data, dict):
                result = {}
                for key, value in data.items():
                    # Rename total_tokens_sum back to total_tokens
                    if key == 'total_tokens_sum':
                        key = 'total_tokens'
                    result[key] = convert_decimals(value)
                return result
            elif hasattr(data, '__float__'):  # Handle Decimal values
                return float(data)
            else:
                return data

        return Response({
            'summary': convert_decimals(summary),
            'model_breakdown': convert_decimals(list(model_breakdown)),
            'api_type_breakdown': convert_decimals(list(api_type_breakdown)),
            'daily_usage': convert_decimals(list(daily_usage)),
            'date_range': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None,
            }
        })
        
    except Exception as e:
        return Response(
            {"error": f"An error occurred: {str(e)}"}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAdminUser])
def usage_logs(request):
    """
    Get detailed usage logs with pagination and filtering.
    
    Query parameters:
    - page: Page number (default: 1)
    - page_size: Items per page (default: 50, max: 1000)
    - start_date: YYYY-MM-DD format (optional)
    - end_date: YYYY-MM-DD format (optional)
    - model_name: Filter by model name (optional)
    - api_type: Filter by API type (optional)
    - user_id: Filter by user ID (optional)
    """
    try:
        # Parse pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 50)), 1000)
        
        # Parse filter parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        model_name = request.GET.get('model_name')
        api_type = request.GET.get('api_type')
        user_id = request.GET.get('user_id')
        
        # Build queryset
        queryset = OpenAIUsage.objects.all()
        
        # Apply filters
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
                start_date = timezone.make_aware(start_date)
                queryset = queryset.filter(request_timestamp__gte=start_date)
            except ValueError:
                return Response(
                    {"error": "Invalid start_date format. Use YYYY-MM-DD."}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
                end_date = timezone.make_aware(end_date)
                end_date = end_date.replace(hour=23, minute=59, second=59)
                queryset = queryset.filter(request_timestamp__lte=end_date)
            except ValueError:
                return Response(
                    {"error": "Invalid end_date format. Use YYYY-MM-DD."}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        if model_name:
            queryset = queryset.filter(model_name__icontains=model_name)
        
        if api_type:
            queryset = queryset.filter(api_type=api_type)
        
        if user_id:
            try:
                user_id = int(user_id)
                queryset = queryset.filter(user_id=user_id)
            except ValueError:
                return Response(
                    {"error": "Invalid user_id. Must be an integer."}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Get total count
        total_count = queryset.count()
        
        # Apply pagination
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        logs = queryset[start_index:end_index]
        
        # Serialize data
        logs_data = []
        for log in logs:
            logs_data.append({
                'id': log.id,
                'model_name': log.model_name,
                'api_type': log.api_type,
                'prompt_tokens': log.prompt_tokens,
                'completion_tokens': log.completion_tokens,
                'total_tokens': log.total_tokens,
                'cost_usd': float(log.cost_usd),
                'user_id': log.user_id,
                'user_name': log.user.name if log.user else None,
                'request_timestamp': log.request_timestamp.isoformat(),
                'response_time_ms': log.response_time_ms,
                'purpose': log.purpose,
            })
        
        # Calculate pagination info
        total_pages = (total_count + page_size - 1) // page_size
        has_next = page < total_pages
        has_previous = page > 1
        
        return Response({
            'logs': logs_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_next': has_next,
                'has_previous': has_previous,
            }
        })
        
    except Exception as e:
        return Response(
            {"error": f"An error occurred: {str(e)}"}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAdminUser])
def export_usage_csv(request):
    """
    Export usage logs as CSV file.
    
    Query parameters (same as usage_logs):
    - start_date: YYYY-MM-DD format (optional)
    - end_date: YYYY-MM-DD format (optional)
    - model_name: Filter by model name (optional)
    - api_type: Filter by API type (optional)
    - user_id: Filter by user ID (optional)
    """
    try:
        # Parse filter parameters (same as usage_logs)
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        model_name = request.GET.get('model_name')
        api_type = request.GET.get('api_type')
        user_id = request.GET.get('user_id')
        
        # Build queryset (same logic as usage_logs)
        queryset = OpenAIUsage.objects.all()
        
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            start_date = timezone.make_aware(start_date)
            queryset = queryset.filter(request_timestamp__gte=start_date)
        
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
            end_date = timezone.make_aware(end_date)
            end_date = end_date.replace(hour=23, minute=59, second=59)
            queryset = queryset.filter(request_timestamp__lte=end_date)
        
        if model_name:
            queryset = queryset.filter(model_name__icontains=model_name)
        
        if api_type:
            queryset = queryset.filter(api_type=api_type)
        
        if user_id:
            user_id = int(user_id)
            queryset = queryset.filter(user_id=user_id)
        
        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename="openai_usage_{timestamp}.csv"'
        
        writer = csv.writer(response)
        
        # Write header
        writer.writerow([
            'ID',
            'Timestamp',
            'Model Name',
            'API Type',
            'Prompt Tokens',
            'Completion Tokens',
            'Total Tokens',
            'Cost (USD)',
            'User ID',
            'User Name',
            'Response Time (ms)',
            'Purpose'
        ])
        
        # Write data
        for log in queryset.iterator():
            writer.writerow([
                log.id,
                log.request_timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                log.model_name,
                log.api_type,
                log.prompt_tokens,
                log.completion_tokens,
                log.total_tokens,
                float(log.cost_usd),
                log.user_id,
                log.user.name if log.user else '',
                log.response_time_ms,
                log.purpose,
            ])
        
        return response
        
    except Exception as e:
        return JsonResponse(
            {"error": f"An error occurred: {str(e)}"}, 
            status=500
        )
