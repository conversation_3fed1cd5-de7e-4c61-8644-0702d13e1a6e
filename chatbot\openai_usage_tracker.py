"""
OpenAI Usage Tracking Module

This module provides wrapper functions to track OpenAI API usage
and automatically log usage data to the database for cost monitoring.
"""

import time
import openai
from decimal import Decimal
from django.utils import timezone
from .models import OpenAIUsage


# OpenAI Pricing (as of 2024) - Update these as needed
OPENAI_PRICING = {
    # Chat Completion Models (per 1K tokens)
    'gpt-4o-mini': {
        'input': Decimal('0.000150'),   # $0.150 per 1M input tokens
        'output': Decimal('0.000600'),  # $0.600 per 1M output tokens
    },
    'gpt-4o': {
        'input': Decimal('0.005000'),   # $5.00 per 1M input tokens
        'output': Decimal('0.015000'),  # $15.00 per 1M output tokens
    },
    'gpt-4': {
        'input': Decimal('0.030000'),   # $30.00 per 1M input tokens
        'output': Decimal('0.060000'),  # $60.00 per 1M output tokens
    },
    'gpt-3.5-turbo': {
        'input': Decimal('0.001500'),   # $1.50 per 1M input tokens
        'output': Decimal('0.002000'),  # $2.00 per 1M output tokens
    },
    
    # Embedding Models (per 1K tokens)
    'text-embedding-ada-002': {
        'input': Decimal('0.000100'),   # $0.10 per 1M tokens
        'output': Decimal('0.000000'),  # No output cost for embeddings
    },
    'text-embedding-3-small': {
        'input': Decimal('0.000020'),   # $0.02 per 1M tokens
        'output': Decimal('0.000000'),
    },
    'text-embedding-3-large': {
        'input': Decimal('0.000130'),   # $0.13 per 1M tokens
        'output': Decimal('0.000000'),
    },
}


def calculate_cost(model_name, prompt_tokens, completion_tokens=0):
    """
    Calculate the cost of an OpenAI API call based on token usage.
    
    Args:
        model_name (str): Name of the OpenAI model
        prompt_tokens (int): Number of input/prompt tokens
        completion_tokens (int): Number of output/completion tokens
    
    Returns:
        Decimal: Cost in USD
    """
    if model_name not in OPENAI_PRICING:
        # Default fallback pricing for unknown models
        return Decimal('0.001000') * (prompt_tokens + completion_tokens) / 1000
    
    pricing = OPENAI_PRICING[model_name]
    input_cost = (Decimal(prompt_tokens) / 1000) * pricing['input']
    output_cost = (Decimal(completion_tokens) / 1000) * pricing['output']
    
    return input_cost + output_cost


def track_openai_chat_completion(model, messages, user=None, purpose="chat_response", **kwargs):
    """
    Wrapper for OpenAI ChatCompletion.create that tracks usage.
    
    Args:
        model (str): OpenAI model name
        messages (list): Chat messages
        user (CustomUser, optional): User making the request
        purpose (str): Purpose of the API call
        **kwargs: Additional arguments for OpenAI API
    
    Returns:
        OpenAI response object
    """
    start_time = time.time()
    
    try:
        # Make the OpenAI API call
        response = openai.ChatCompletion.create(
            model=model,
            messages=messages,
            **kwargs
        )
        
        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Extract usage information
        usage = response.get('usage', {})
        prompt_tokens = usage.get('prompt_tokens', 0)
        completion_tokens = usage.get('completion_tokens', 0)
        total_tokens = usage.get('total_tokens', prompt_tokens + completion_tokens)
        
        # Calculate cost
        cost = calculate_cost(model, prompt_tokens, completion_tokens)
        
        # Save usage data to database
        OpenAIUsage.objects.create(
            model_name=model,
            api_type='chat_completion',
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            cost_usd=cost,
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=purpose
        )
        
        return response
        
    except Exception as e:
        # Log failed requests with minimal data
        response_time_ms = int((time.time() - start_time) * 1000)
        OpenAIUsage.objects.create(
            model_name=model,
            api_type='chat_completion',
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            cost_usd=Decimal('0.00'),
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=f"{purpose}_failed"
        )
        raise e


def track_openai_embedding(input_text, model="text-embedding-ada-002", user=None, purpose="embedding_generation", **kwargs):
    """
    Wrapper for OpenAI Embedding.create that tracks usage.
    
    Args:
        input_text (str or list): Text to embed
        model (str): OpenAI embedding model name
        user (CustomUser, optional): User making the request
        purpose (str): Purpose of the API call
        **kwargs: Additional arguments for OpenAI API
    
    Returns:
        OpenAI response object
    """
    start_time = time.time()
    
    try:
        # Make the OpenAI API call
        response = openai.Embedding.create(
            input=input_text,
            model=model,
            **kwargs
        )
        
        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Extract usage information
        usage = response.get('usage', {})
        prompt_tokens = usage.get('prompt_tokens', 0)
        total_tokens = usage.get('total_tokens', prompt_tokens)
        
        # Calculate cost
        cost = calculate_cost(model, prompt_tokens, 0)
        
        # Save usage data to database
        OpenAIUsage.objects.create(
            model_name=model,
            api_type='embedding',
            prompt_tokens=prompt_tokens,
            completion_tokens=0,
            total_tokens=total_tokens,
            cost_usd=cost,
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=purpose
        )
        
        return response
        
    except Exception as e:
        # Log failed requests with minimal data
        response_time_ms = int((time.time() - start_time) * 1000)
        OpenAIUsage.objects.create(
            model_name=model,
            api_type='embedding',
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            cost_usd=Decimal('0.00'),
            user=user,
            request_timestamp=timezone.now(),
            response_time_ms=response_time_ms,
            purpose=f"{purpose}_failed"
        )
        raise e


def get_usage_summary(start_date=None, end_date=None):
    """
    Get usage summary statistics.

    Args:
        start_date (datetime, optional): Start date for filtering
        end_date (datetime, optional): End date for filtering

    Returns:
        dict: Usage summary statistics
    """
    try:
        from django.db.models import Sum, Count, Avg

        queryset = OpenAIUsage.objects.all()

        if start_date:
            queryset = queryset.filter(request_timestamp__gte=start_date)
        if end_date:
            queryset = queryset.filter(request_timestamp__lte=end_date)

        summary = queryset.aggregate(
            total_requests=Count('id'),
            total_tokens_sum=Sum('total_tokens'),
            total_cost=Sum('cost_usd'),
            avg_tokens_per_request=Avg('total_tokens'),
            avg_cost_per_request=Avg('cost_usd'),
            total_prompt_tokens=Sum('prompt_tokens'),
            total_completion_tokens=Sum('completion_tokens'),
        )

        # Rename the sum field to match expected output
        if 'total_tokens_sum' in summary:
            summary['total_tokens'] = summary.pop('total_tokens_sum')

        # Convert None values to 0 and Decimal to float for JSON serialization
        for key, value in summary.items():
            if value is None:
                summary[key] = 0
            elif hasattr(value, '__float__'):  # Handle Decimal values
                summary[key] = float(value)

        return summary

    except Exception as e:
        # Return default empty summary if there's an error
        return {
            'total_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'avg_tokens_per_request': 0.0,
            'avg_cost_per_request': 0.0,
            'total_prompt_tokens': 0,
            'total_completion_tokens': 0,
        }
