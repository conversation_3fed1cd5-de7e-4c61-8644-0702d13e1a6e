import uuid
import mysql.connector
from typing import List
import weaviate
import openai
import argparse
import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_chatbot_backend.settings')
django.setup()

from chatbot.openai_usage_tracker import track_openai_embedding

# --- Configuration ---
WEAVIATE_URL = "http://localhost:8080"
WEAVIATE_CLASS_NAME = "ChunkEmbeddingsV2"
EMBEDDING_BATCH_SIZE = 1

# OpenAI & Weaviate
openai.api_key = "********************************************************************************************************************************************************************"
client_weaviate = weaviate.Client(WEAVIATE_URL)

# MySQL DB config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'vishal2004',
    'database': 'rough'
}

def get_chunks_from_db() -> List[dict]:
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)
    cursor.execute("SELECT id, source_file, chunk_number, content FROM pdf_chunks WHERE vector_embedded = 0")
    rows = cursor.fetchall()
    cursor.close()
    conn.close()
    return rows

def update_vectorized_flag(chunk_ids: List[int]):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    format_ids = ",".join(["%s"] * len(chunk_ids))
    cursor.execute(f"UPDATE pdf_chunks SET vector_embedded = 1 WHERE id IN ({format_ids})", chunk_ids)
    conn.commit()
    cursor.close()
    conn.close()

def get_embeddings_batch(texts: List[str]) -> List[List[float]]:
    response = track_openai_embedding(
        input_text=texts,
        model="text-embedding-ada-002",
        user=None,
        purpose="batch_embedding_generation"
    )
    return [item.embedding for item in response.data]

def setup_weaviate_schema(client: weaviate.Client):
    schema = client.schema.get()
    classes = [c["class"] for c in schema.get("classes", [])]
    if WEAVIATE_CLASS_NAME not in classes:
        class_obj = {
            "class": WEAVIATE_CLASS_NAME,
            "description": "Stores embeddings for chunked text.",
            "properties": [
                {"name": "source_file", "dataType": ["string"]},
                {"name": "chunk_number", "dataType": ["int"]},
                {"name": "content", "dataType": ["text"]}
            ],
            "vectorizer": "none"
        }
        client.schema.create_class(class_obj)
        print(f"Created class '{WEAVIATE_CLASS_NAME}'.")

def batch_chunks(chunks: List[dict], batch_size: int):
    for i in range(0, len(chunks), batch_size):
        yield chunks[i:i + batch_size]

def store_embeddings(chunks: List[dict]):
    print(f"Processing {len(chunks)} chunks in batches of {EMBEDDING_BATCH_SIZE}...")

    for batch in batch_chunks(chunks, EMBEDDING_BATCH_SIZE):
        valid_chunks = [chunk for chunk in batch if chunk.get("content")]
        if not valid_chunks:
            continue

        texts = [chunk["content"] for chunk in valid_chunks]

        try:
            embeddings = get_embeddings_batch(texts)
        except Exception as e:
            print(f"Embedding failed for batch: {e}")
            continue

        successful_ids = []

        for idx in range(len(valid_chunks)):
            chunk = valid_chunks[idx]
            try:
                embedding = embeddings[idx]
                obj_uuid = str(uuid.uuid5(uuid.NAMESPACE_URL, chunk["content"]))

                # Skip if already exists in Weaviate
                if client_weaviate.data_object.exists(uuid=obj_uuid):
                    print(f"Chunk already exists in Weaviate: {chunk['chunk_number']} from {chunk['source_file']}")
                    continue

                data_object = {
                    "source_file": chunk["source_file"],
                    "chunk_number": int(chunk["chunk_number"]),
                    "content": chunk["content"]
                }

                client_weaviate.data_object.create(
                    data_object=data_object,
                    class_name=WEAVIATE_CLASS_NAME,
                    vector=embedding,
                    uuid=obj_uuid
                )

                successful_ids.append(chunk["id"])
                print(f"Vectorized chunk {chunk['chunk_number']} from {chunk['source_file']}")
            except Exception as e:
                print(f"Failed to vectorize chunk: {e}")
                continue

        if successful_ids:
            update_vectorized_flag(successful_ids)

def main(args):
    if args.weaviate:
        setup_weaviate_schema(client_weaviate)
        chunks = get_chunks_from_db()
        if not chunks:
            print("No chunks to vectorize.")
            return
        store_embeddings(chunks)
    else:
        print("Use --weaviate to start embedding.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--weaviate", action="store_true")
    args = parser.parse_args()
    main(args)